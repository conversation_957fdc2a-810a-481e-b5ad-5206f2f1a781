from flask import Flask, render_template, request, redirect, url_for, session, jsonify, g
from flask_wtf.csrf import CSRFProtect
import sqlite3
from werkzeug.security import generate_password_hash, check_password_hash
import datetime
import time
from database import get_db, init_db
from zk_biometric import sync_attendance_from_device, ZKBiometricDevice, verify_staff_biometric, process_device_attendance_automatically
import os

app = Flask(__name__)
app.secret_key = os.environ.get('SECRET_KEY', os.urandom(24).hex())

# Initialize CSRF protection
csrf = CSRFProtect(app)

# Initialize database with the app
init_db(app)  # Pass the app instance here

# Custom Jinja2 filter for date formatting
@app.template_filter('dateformat')
def dateformat(value, format='%m/%d/%Y'):
    """Format a date string or date object"""
    if value is None:
        return ''

    # If it's already a string (from SQLite), try to parse it
    if isinstance(value, str):
        try:
            # SQLite stores dates as YYYY-MM-DD
            date_obj = datetime.datetime.strptime(value, '%Y-%m-%d').date()
            return date_obj.strftime(format)
        except ValueError:
            # If parsing fails, return the original string
            return value

    # If it's a date or datetime object, format it directly
    if hasattr(value, 'strftime'):
        return value.strftime(format)

    # Fallback: return as string
    return str(value)

# Custom Jinja2 filter for datetime formatting
@app.template_filter('datetimeformat')
def datetimeformat(value, format='%m/%d %H:%M'):
    """Format a datetime string or datetime object"""
    if value is None:
        return ''

    # If it's already a string (from SQLite), try to parse it
    if isinstance(value, str):
        try:
            # Try different SQLite datetime formats
            for fmt in ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d %H:%M:%S.%f']:
                try:
                    datetime_obj = datetime.datetime.strptime(value, fmt)
                    return datetime_obj.strftime(format)
                except ValueError:
                    continue
            # If parsing fails, return the original string
            return value
        except:
            return value

    # If it's a datetime object, format it directly
    if hasattr(value, 'strftime'):
        return value.strftime(format)

    # Fallback: return as string
    return str(value)

# Helper functions
def get_schools():
    db = get_db()
    schools = db.execute('SELECT id, name FROM schools ORDER BY name').fetchall()
    return schools

def get_pending_leaves():
    """Get pending leave applications for the current admin's school"""
    if 'school_id' not in session:
        return []

    db = get_db()
    return db.execute('''
        SELECT la.*, s.full_name as staff_name
        FROM leave_applications la
        JOIN staff s ON la.staff_id = s.id
        WHERE la.school_id = ? AND la.status = 'pending'
        ORDER BY la.applied_at DESC
    ''', (session['school_id'],)).fetchall()

def get_today_attendance():
    """Get today's attendance summary for the current admin's school"""
    if 'school_id' not in session:
        return {'present': 0, 'absent': 0, 'late': 0, 'total': 0}

    db = get_db()
    today = datetime.date.today()

    # Get total staff count
    total_staff = db.execute('''
        SELECT COUNT(*) as count FROM staff WHERE school_id = ?
    ''', (session['school_id'],)).fetchone()['count']

    # Get attendance counts
    attendance_counts = db.execute('''
        SELECT status, COUNT(*) as count
        FROM attendance
        WHERE school_id = ? AND date = ?
        GROUP BY status
    ''', (session['school_id'], today)).fetchall()

    summary = {'present': 0, 'absent': 0, 'late': 0, 'total': total_staff}
    for row in attendance_counts:
        if row['status'] in summary:
            summary[row['status']] = row['count']

    return summary

# File upload configuration
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.teardown_appcontext
def close_db(error):
    """Close database connection at the end of request"""
    _ = error  # Suppress unused parameter warning
    db = getattr(g, '_database', None)
    if db is not None:
        db.close()

# In app.py, update the index route
@app.route('/')
def index():
    db = get_db()
    
    # First check if the column exists
    columns = db.execute("PRAGMA table_info(schools)").fetchall()
    has_is_hidden = any(col['name'] == 'is_hidden' for col in columns)
    
    if has_is_hidden:
        schools = db.execute('SELECT id, name FROM schools WHERE is_hidden = 0 OR is_hidden IS NULL ORDER BY name').fetchall()
    else:
        schools = db.execute('SELECT id, name FROM schools ORDER BY name').fetchall()
    
    return render_template('index.html', schools=schools)

# Routes
@app.route('/company_login', methods=['GET', 'POST'])
def handle_company_login():
    if request.method == 'GET':
        return render_template('company_login.html')
    
    # Handle POST request
    username = request.form.get('username')
    password = request.form.get('password')
    
    db = get_db()
    company_admin = db.execute('''
        SELECT * FROM company_admins WHERE username = ?
    ''', (username,)).fetchone()
    
    if not company_admin:
        return jsonify({'error': 'Company admin not found'}), 401
    
    if not check_password_hash(company_admin['password'], password):
        return jsonify({'error': 'Invalid password'}), 401
    
    session['user_id'] = company_admin['id']
    session['user_type'] = 'company_admin'
    session['full_name'] = company_admin['full_name']
    return jsonify({'redirect': url_for('company_dashboard')})

@app.route('/login', methods=['POST'])
def handle_school_login():
    school_id = request.form.get('school_id')
    username = request.form.get('username')
    password = request.form.get('password')
    
    print(f"Login attempt - School ID: {school_id}, Username: {username}")  # Debug log
    
    if not school_id:
        return jsonify({'error': 'Please select a school'}), 400

    db = get_db()
    
    # Check school admin
    admin = db.execute('''
        SELECT * FROM admins 
        WHERE school_id = ? AND username = ?
    ''', (school_id, username)).fetchone()
    
    if admin and check_password_hash(admin['password'], password):
        print("Admin login successful")  # Debug log
        session['user_id'] = admin['id']
        session['school_id'] = admin['school_id']
        session['user_type'] = 'admin'
        session['full_name'] = admin['full_name']
        return jsonify({'redirect': url_for('admin_dashboard')})
    
    # Check staff - using username as staff_id
    staff = db.execute('''
        SELECT * FROM staff 
        WHERE school_id = ? AND staff_id = ?
    ''', (school_id, username)).fetchone()
    
    if staff:
        password_hash = staff['password_hash'] if staff['password_hash'] is not None else ''
        if check_password_hash(password_hash, password):
            print("Staff login successful")  # Debug log
            session['user_id'] = staff['id']
            session['school_id'] = staff['school_id']
            session['user_type'] = 'staff'
            session['full_name'] = staff['full_name']
            return jsonify({'redirect': url_for('staff_dashboard')})
    
    print("Login failed - invalid credentials")  # Debug log
    return jsonify({'error': 'Invalid credentials'}), 401

# Add these new routes to app.py

@app.route('/company/school_details/<int:school_id>')
def school_details(school_id):
    if 'user_id' not in session or session['user_type'] != 'company_admin':
        return redirect(url_for('index'))
    
    db = get_db()
    
    # Get school info
    school = db.execute('SELECT * FROM schools WHERE id = ?', (school_id,)).fetchone()
    if not school:
        return redirect(url_for('company_dashboard'))
    
    # Get admins
    admins = db.execute('''
        SELECT id, username, full_name, email 
        FROM admins 
        WHERE school_id = ?
    ''', (school_id,)).fetchall()
    
    # Get staff
    staff = db.execute('''
        SELECT id, staff_id, full_name, department, position, email, phone 
        FROM staff 
        WHERE school_id = ?
        ORDER BY full_name
    ''', (school_id,)).fetchall()
    
    # Get attendance summary
    today = datetime.date.today()
    attendance_summary = db.execute('''
        SELECT 
            COUNT(*) as total_staff,
            SUM(CASE WHEN a.status = 'present' THEN 1 ELSE 0 END) as present,
            SUM(CASE WHEN a.status = 'absent' THEN 1 ELSE 0 END) as absent,
            SUM(CASE WHEN a.status = 'late' THEN 1 ELSE 0 END) as late,
            SUM(CASE WHEN a.status = 'leave' THEN 1 ELSE 0 END) as on_leave
        FROM (
            SELECT s.id, COALESCE(a.status, 'absent') as status
            FROM staff s
            LEFT JOIN attendance a ON s.id = a.staff_id AND a.date = ?
            WHERE s.school_id = ?
        ) a
    ''', (today, school_id)).fetchone()
    
    # Get pending leaves
    pending_leaves = db.execute('''
        SELECT l.id, s.full_name, l.leave_type, l.start_date, l.end_date, l.reason
        FROM leave_applications l
        JOIN staff s ON l.staff_id = s.id
        WHERE l.school_id = ? AND l.status = 'pending'
        ORDER BY l.applied_at
    ''', (school_id,)).fetchall()
    
    return render_template('school_details.html',
                         school=school,
                         admins=admins,
                         staff=staff,
                         attendance_summary=attendance_summary,
                         pending_leaves=pending_leaves,
                         today=today)

@app.route('/get_attendance_summary')
def get_attendance_summary():
    if 'user_id' not in session:
        return jsonify({'success': False, 'error': 'Unauthorized'})
    
    db = get_db()
    
    if session['user_type'] == 'staff':
        staff_id = session['user_id']
        today = datetime.date.today()
        
        # Get current month attendance
        first_day = today.replace(day=1)
        last_day = (today.replace(day=28) + datetime.timedelta(days=4)).replace(day=1) - datetime.timedelta(days=1)
        
        attendance = db.execute('''
            SELECT status, COUNT(*) as count 
            FROM attendance 
            WHERE staff_id = ? AND date BETWEEN ? AND ?
            GROUP BY status
        ''', (staff_id, first_day, last_day)).fetchall()
        
        # Initialize counts
        present = 0
        absent = 0
        late = 0
        leave = 0
        
        for record in attendance:
            if record['status'] == 'present':
                present = record['count']
            elif record['status'] == 'absent':
                absent = record['count']
            elif record['status'] == 'late':
                late = record['count']
            elif record['status'] == 'leave':
                leave = record['count']
        
        return jsonify({
            'success': True,
            'present': present,
            'absent': absent,
            'late': late,
            'leave': leave
        })
    
    return jsonify({'success': False, 'error': 'Unauthorized'})

@app.route('/get_staff_details')
def get_staff_details():
    if 'user_id' not in session or session['user_type'] != 'admin':
        return jsonify({'success': False, 'error': 'Unauthorized'})
    
    staff_id = request.args.get('id')
    db = get_db()
    
    staff = db.execute('SELECT * FROM staff WHERE id = ?', (staff_id,)).fetchone()
    if not staff:
        return jsonify({'success': False, 'error': 'Staff not found'})
    
    # Get attendance records
    attendance = db.execute('''
        SELECT date, time_in, time_out, status 
        FROM attendance 
        WHERE staff_id = ?
        ORDER BY date DESC
    ''', (staff_id,)).fetchall()
    
    return jsonify({
        'success': True,
        'staff': dict(staff),
        'attendance': [dict(a) for a in attendance]
    })


@app.route('/export_staff_data')
def export_staff_data():
    if 'user_id' not in session or session['user_type'] != 'admin':
        return jsonify({'success': False, 'error': 'Unauthorized'})
    
    school_id = session['school_id']
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    
    db = get_db()
    
    # Get staff and their attendance for the date range
    staff = db.execute('''
        SELECT s.id, s.staff_id, s.full_name, s.department, s.position,
               SUM(CASE WHEN a.status = 'present' THEN 1 ELSE 0 END) as present_days,
               SUM(CASE WHEN a.status = 'absent' THEN 1 ELSE 0 END) as absent_days,
               SUM(CASE WHEN a.status = 'late' THEN 1 ELSE 0 END) as late_days,
               SUM(CASE WHEN a.status = 'leave' THEN 1 ELSE 0 END) as leave_days,
               SUM(CASE WHEN l.leave_type = 'CL' THEN 1 ELSE 0 END) as casual_leave,
               SUM(CASE WHEN l.leave_type = 'SL' THEN 1 ELSE 0 END) as sick_leave,
               SUM(CASE WHEN l.leave_type = 'EL' THEN 1 ELSE 0 END) as earned_leave,
               SUM(CASE WHEN l.leave_type = 'ML' THEN 1 ELSE 0 END) as maternity_leave
        FROM staff s
        LEFT JOIN attendance a ON s.id = a.staff_id AND a.date BETWEEN ? AND ?
        LEFT JOIN leave_applications l ON s.id = l.staff_id AND l.status = 'approved' 
            AND l.start_date <= ? AND l.end_date >= ?
        WHERE s.school_id = ?
        GROUP BY s.id
        ORDER BY s.full_name
    ''', (start_date, end_date, end_date, start_date, school_id)).fetchall()
    
    # Create CSV
    import csv
    from io import StringIO
    
    output = StringIO()
    writer = csv.writer(output)
    
    # Write header
    writer.writerow(['S.No', 'Name', 'Department', 'Position', 
                    'Present Days', 'Absent Days', 'Late Days', 'Leave Days',
                    'Casual Leave', 'Sick Leave', 'Earned Leave', 'Maternity Leave'])
    
    # Write data
    for i, member in enumerate(staff, 1):
        writer.writerow([
            i,
            member['full_name'],
            member['department'],
            member['position'],
            member['present_days'],
            member['absent_days'],
            member['late_days'],
            member['leave_days'],
            member['casual_leave'],
            member['sick_leave'],
            member['earned_leave'],
            member['maternity_leave']
        ])
    
    from flask import make_response
    response = make_response(output.getvalue())
    filename = f'staff_attendance_{start_date}_to_{end_date}.csv' if start_date and end_date else 'staff_data.csv'
    response.headers['Content-Disposition'] = f'attachment; filename={filename}'
    response.headers['Content-type'] = 'text/csv'
    return response

@app.route('/add_admin', methods=['POST'])
def add_admin():
    if 'user_id' not in session or session['user_type'] != 'company_admin':
        return jsonify({'success': False, 'error': 'Unauthorized'})
    
    school_id = request.form.get('school_id')
    username = request.form.get('username')
    password = generate_password_hash(request.form.get('password'))
    full_name = request.form.get('full_name')
    email = request.form.get('email')
    
    db = get_db()
    
    try:
        db.execute('''
            INSERT INTO admins (school_id, username, password, full_name, email)
            VALUES (?, ?, ?, ?, ?)
        ''', (school_id, username, password, full_name, email))
        db.commit()
        return jsonify({'success': True})
    except sqlite3.IntegrityError:
        return jsonify({'success': False, 'error': 'Username already exists'})

@app.route('/delete_admin', methods=['POST'])
def delete_admin():
    if 'user_id' not in session or session['user_type'] != 'company_admin':
        return jsonify({'success': False, 'error': 'Unauthorized'})
    
    admin_id = request.form.get('admin_id')
    
    db = get_db()
    
    db.execute('DELETE FROM admins WHERE id = ?', (admin_id,))
    db.commit()
    
    return jsonify({'success': True})

@app.route('/get_staff_attendance')
def get_staff_attendance():
    if 'user_id' not in session or session['user_type'] != 'staff':
        return jsonify({'success': False, 'error': 'Unauthorized'})

    staff_id = session['user_id']
    start_date = request.args.get('start')
    end_date = request.args.get('end')

    db = get_db()

    attendance = db.execute('''
        SELECT date, time_in, time_out, overtime_in, overtime_out, status
        FROM attendance
        WHERE staff_id = ? AND date BETWEEN ? AND ?
        ORDER BY date
    ''', (staff_id, start_date, end_date)).fetchall()

    # In a real app, you would fetch holidays from a database
    holidays = []  # Example: [{'date': '2023-01-01', 'name': 'New Year'}]

    return jsonify({
        'success': True,
        'attendance': [dict(a) for a in attendance],
        'holidays': holidays
    })

@app.route('/get_biometric_verifications')
def get_biometric_verifications():
    """Get biometric verification history for staff"""
    if 'user_id' not in session or session['user_type'] != 'staff':
        return jsonify({'success': False, 'error': 'Unauthorized'})

    staff_id = session['user_id']
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    db = get_db()

    # Get verification history
    query = '''
        SELECT verification_type, verification_time, biometric_method,
               verification_status, notes, device_ip
        FROM biometric_verifications
        WHERE staff_id = ?
    '''
    params = [staff_id]

    if start_date and end_date:
        query += ' AND DATE(verification_time) BETWEEN ? AND ?'
        params.extend([start_date, end_date])

    query += ' ORDER BY verification_time DESC LIMIT 50'

    verifications = db.execute(query, params).fetchall()

    return jsonify({
        'success': True,
        'verifications': [dict(v) for v in verifications]
    })

@app.route('/get_today_attendance_status')
def get_today_attendance_status():
    """Get today's attendance status for staff"""
    if 'user_id' not in session or session['user_type'] != 'staff':
        return jsonify({'success': False, 'error': 'Unauthorized'})

    staff_id = session['user_id']
    today = datetime.date.today()

    db = get_db()

    # Get today's attendance
    attendance = db.execute('''
        SELECT time_in, time_out, overtime_in, overtime_out, status
        FROM attendance
        WHERE staff_id = ? AND date = ?
    ''', (staff_id, today)).fetchone()

    # Get today's verifications
    verifications = db.execute('''
        SELECT verification_type, verification_time, biometric_method, verification_status
        FROM biometric_verifications
        WHERE staff_id = ? AND DATE(verification_time) = ?
        ORDER BY verification_time DESC
    ''', (staff_id, today)).fetchall()

    # Determine available actions based on current status
    available_actions = []
    if not attendance or not attendance['time_in']:
        available_actions.append('check-in')
    if attendance and attendance['time_in'] and not attendance['time_out']:
        available_actions.append('check-out')
    if attendance and attendance['time_out'] and not attendance['overtime_in']:
        available_actions.append('overtime-in')
    if attendance and attendance['overtime_in'] and not attendance['overtime_out']:
        available_actions.append('overtime-out')

    return jsonify({
        'success': True,
        'attendance': dict(attendance) if attendance else None,
        'verifications': [dict(v) for v in verifications],
        'available_actions': available_actions
    })

@app.route('/get_realtime_attendance')
def get_realtime_attendance():
    """Get real-time attendance data for admin dashboard"""
    if 'user_id' not in session or session['user_type'] not in ['admin', 'company_admin']:
        return jsonify({'success': False, 'error': 'Unauthorized'})

    school_id = session.get('school_id', 1)
    today = datetime.date.today()

    db = get_db()

    # Get today's attendance details for all staff
    today_attendance = db.execute('''
        SELECT s.id as staff_id, s.staff_id as staff_number, s.full_name, s.department,
               a.time_in, a.time_out, a.overtime_in, a.overtime_out,
               COALESCE(a.status, 'absent') as status
        FROM staff s
        LEFT JOIN attendance a ON s.id = a.staff_id AND a.date = ?
        WHERE s.school_id = ?
        ORDER BY s.full_name
    ''', (today, school_id)).fetchall()

    # Get attendance summary
    attendance_summary = db.execute('''
        SELECT
            COUNT(*) as total_staff,
            SUM(CASE WHEN a.status = 'present' THEN 1 ELSE 0 END) as present,
            SUM(CASE WHEN a.status = 'absent' THEN 1 ELSE 0 END) as absent,
            SUM(CASE WHEN a.status = 'late' THEN 1 ELSE 0 END) as late,
            SUM(CASE WHEN a.status = 'leave' THEN 1 ELSE 0 END) as on_leave
        FROM (
            SELECT s.id, COALESCE(a.status, 'absent') as status
            FROM staff s
            LEFT JOIN attendance a ON s.id = a.staff_id AND a.date = ?
            WHERE s.school_id = ?
        ) a
    ''', (today, school_id)).fetchone()

    return jsonify({
        'success': True,
        'attendance_data': [dict(row) for row in today_attendance],
        'summary': dict(attendance_summary) if attendance_summary else {},
        'timestamp': datetime.datetime.now().isoformat()
    })

@app.route('/export_company_report')
def export_company_report():
    if 'user_id' not in session or session['user_type'] != 'company_admin':
        return jsonify({'success': False, 'error': 'Unauthorized'})
    
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    
    if not start_date or not end_date:
        return jsonify({'success': False, 'error': 'Date range is required'})
    
    try:
        start_date = datetime.datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date = datetime.datetime.strptime(end_date, '%Y-%m-%d').date()
    except ValueError:
        return jsonify({'success': False, 'error': 'Invalid date format. Use YYYY-MM-DD'})
    
    db = get_db()
    
    # Get school data with attendance counts for the date range
    schools = db.execute('''
        SELECT 
            s.id,
            s.name as school_name,
            COUNT(DISTINCT a.id) as admin_count,
            COUNT(DISTINCT st.id) as staff_count,
            ROUND(AVG(
                CASE WHEN at.date BETWEEN ? AND ? THEN
                    CASE WHEN at.status = 'present' THEN 1 ELSE 0 END
                ELSE NULL END
            ), 2) as avg_attendance_rate
        FROM schools s
        LEFT JOIN admins a ON s.id = a.school_id
        LEFT JOIN staff st ON s.id = st.school_id
        LEFT JOIN attendance at ON st.id = at.staff_id AND at.date BETWEEN ? AND ?
        GROUP BY s.id
        ORDER BY s.name
    ''', (start_date, end_date, start_date, end_date)).fetchall()
    
    # Create CSV
    import csv
    from io import StringIO
    
    output = StringIO()
    writer = csv.writer(output)
    
    # Write header
    writer.writerow(['S.No', 'School Name', 'Number of Admins', 'Number of Staff', 
                    f'Avg Attendance Rate ({start_date} to {end_date})'])
    
    # Write data
    for i, school in enumerate(schools, 1):
        writer.writerow([
            i,
            school['school_name'],
            school['admin_count'],
            school['staff_count'],
            f"{school['avg_attendance_rate'] * 100 if school['avg_attendance_rate'] else 0}%"
        ])
    
    from flask import make_response
    response = make_response(output.getvalue())
    response.headers['Content-Disposition'] = f'attachment; filename=company_report_{start_date}_to_{end_date}.csv'
    response.headers['Content-type'] = 'text/csv'
    return response

@app.route('/export_staff_report')
def export_staff_report():
    if 'user_id' not in session or session['user_type'] != 'staff':
        return jsonify({'success': False, 'error': 'Unauthorized'})
    
    staff_id = session['user_id']
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    
    if not start_date or not end_date:
        return jsonify({'success': False, 'error': 'Date range required'})
    
    db = get_db()
    
    # Get attendance records for the date range
    attendance = db.execute('''
        SELECT date, time_in, time_out, status 
        FROM attendance 
        WHERE staff_id = ? AND date BETWEEN ? AND ?
        ORDER BY date DESC
    ''', (staff_id, start_date, end_date)).fetchall()
    
    # Get summary counts
    summary = db.execute('''
        SELECT 
            SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present,
            SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent,
            SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late,
            SUM(CASE WHEN status = 'leave' THEN 1 ELSE 0 END) as leave
        FROM attendance 
        WHERE staff_id = ? AND date BETWEEN ? AND ?
    ''', (staff_id, start_date, end_date)).fetchone()
    
    # Create CSV
    import csv
    from io import StringIO
    
    output = StringIO()
    writer = csv.writer(output)
    
    # Write header
    writer.writerow(['Date', 'Time In', 'Time Out', 'Status'])
    
    # Write data
    for record in attendance:
        writer.writerow([
            record['date'],
            record['time_in'] or '--:--:--',
            record['time_out'] or '--:--:--',
            record['status']
        ])
    
    # Add summary
    writer.writerow([])
    writer.writerow(['Summary', '', '', ''])
    writer.writerow(['Present Days:', summary['present']])
    writer.writerow(['Absent Days:', summary['absent']])
    writer.writerow(['Late Days:', summary['late']])
    writer.writerow(['Leave Days:', summary['leave']])
    
    from flask import make_response
    response = make_response(output.getvalue())
    response.headers['Content-Disposition'] = f'attachment; filename=attendance_report_{start_date}_to_{end_date}.csv'
    response.headers['Content-type'] = 'text/csv'
    return response

@app.route('/staff/dashboard')
def staff_dashboard():
    if 'user_id' not in session or session['user_type'] != 'staff':
        return redirect(url_for('index'))

    db = get_db()
    staff_db_id = session['user_id']

    # Get staff information including staff_id
    staff_info = db.execute('''
        SELECT staff_id, full_name FROM staff WHERE id = ?
    ''', (staff_db_id,)).fetchone()

    if not staff_info:
        return redirect(url_for('index'))

    # Get attendance records for the current month
    today = datetime.date.today()
    first_day = today.replace(day=1)
    last_day = (today.replace(day=28) + datetime.timedelta(days=4)).replace(day=1) - datetime.timedelta(days=1)

    attendance = db.execute('''
        SELECT date, time_in, time_out, status
        FROM attendance
        WHERE staff_id = ? AND date BETWEEN ? AND ?
        ORDER BY date DESC
    ''', (staff_db_id, first_day, last_day)).fetchall()

    # Get leave applications
    leaves = db.execute('''
        SELECT id, leave_type, start_date, end_date, reason, status
        FROM leave_applications
        WHERE staff_id = ?
        ORDER BY start_date DESC
    ''', (staff_db_id,)).fetchall()

    return render_template('staff_dashboard.html',
                         attendance=attendance,
                         leaves=leaves,
                         today=today,
                         staff_info=staff_info)

@app.route('/admin/dashboard')
def admin_dashboard():
    if 'user_id' not in session or session['user_type'] != 'admin':
        return redirect(url_for('index'))
    
    db = get_db()
    school_id = session['school_id']
    
    # Get all staff
    staff = db.execute('''
        SELECT id, staff_id, full_name, department, position 
        FROM staff 
        WHERE school_id = ?
        ORDER BY full_name
    ''', (school_id,)).fetchall()
    
    # Get pending leave applications
    pending_leaves = db.execute('''
        SELECT l.id, s.full_name, l.leave_type, l.start_date, l.end_date, l.reason
        FROM leave_applications l
        JOIN staff s ON l.staff_id = s.id
        WHERE l.school_id = ? AND l.status = 'pending'
        ORDER BY l.applied_at
    ''', (school_id,)).fetchall()
    
    # Get today's attendance summary
    today = datetime.date.today()
    attendance_summary = db.execute('''
        SELECT
            COUNT(*) as total_staff,
            SUM(CASE WHEN a.status = 'present' THEN 1 ELSE 0 END) as present,
            SUM(CASE WHEN a.status = 'absent' THEN 1 ELSE 0 END) as absent,
            SUM(CASE WHEN a.status = 'late' THEN 1 ELSE 0 END) as late,
            SUM(CASE WHEN a.status = 'leave' THEN 1 ELSE 0 END) as on_leave
        FROM (
            SELECT s.id, COALESCE(a.status, 'absent') as status
            FROM staff s
            LEFT JOIN attendance a ON s.id = a.staff_id AND a.date = ?
            WHERE s.school_id = ?
        ) a
    ''', (today, school_id)).fetchone()

    # Get today's attendance details for all staff
    today_attendance = db.execute('''
        SELECT s.id as staff_id, s.staff_id as staff_number, s.full_name, s.department,
               a.time_in, a.time_out, a.overtime_in, a.overtime_out,
               COALESCE(a.status, 'absent') as status
        FROM staff s
        LEFT JOIN attendance a ON s.id = a.staff_id AND a.date = ?
        WHERE s.school_id = ?
        ORDER BY s.full_name
    ''', (today, school_id)).fetchall()

    return render_template('admin_dashboard.html',
                         staff=staff,
                         pending_leaves=pending_leaves,
                         attendance_summary=attendance_summary,
                         today_attendance=today_attendance,
                         today=today)


@app.route('/company/dashboard')
def company_dashboard():
    if 'user_id' not in session or session['user_type'] != 'company_admin':
        return redirect(url_for('index'))
    
    db = get_db()
    
    # Get all schools
    schools = db.execute('''
        SELECT s.id, s.name, s.address, s.contact_email, s.contact_phone,
               COUNT(a.id) as admin_count,
               COUNT(st.id) as staff_count
        FROM schools s
        LEFT JOIN admins a ON s.id = a.school_id
        LEFT JOIN staff st ON s.id = st.school_id
        GROUP BY s.id
        ORDER BY s.name
    ''').fetchall()
    
    return render_template('company_dashboard.html', schools=schools)

@app.route('/mark_attendance', methods=['POST'])
def mark_attendance():
    """
    LEGACY ROUTE - DEPRECATED
    This route is kept for backward compatibility but should not be used.
    Use /biometric_attendance instead for proper user-controlled verification.
    """
    if 'user_id' not in session or session['user_type'] != 'staff':
        return jsonify({'success': False, 'error': 'Unauthorized'})

    # Return error to prevent automatic updates
    return jsonify({
        'success': False,
        'error': 'This route is deprecated. Please use biometric verification system for attendance marking.'
    })

def validate_verification_rules(verification_type, existing_attendance, current_time):
    """Validate business rules for biometric verification"""

    # Define time thresholds
    LATE_ARRIVAL_TIME = datetime.time(9, 0)  # 9:00 AM
    OVERTIME_START_TIME = datetime.time(17, 0)  # 5:00 PM

    if verification_type == 'check-in':
        # Check-in can be done anytime, but will be marked as late if after LATE_ARRIVAL_TIME
        if existing_attendance and existing_attendance['time_in']:
            return 'Already checked in today'
        # Note: Late arrival is handled in the main function using LATE_ARRIVAL_TIME
        return None

    elif verification_type == 'check-out':
        # Check-out requires prior check-in
        if not existing_attendance or not existing_attendance['time_in']:
            return 'Cannot check-out without checking in first'
        if existing_attendance['time_out']:
            return 'Already checked out today'
        return None

    elif verification_type == 'overtime-in':
        # Overtime-in requires prior check-out and should be after 5 PM
        if not existing_attendance or not existing_attendance['time_out']:
            return 'Cannot start overtime without completing regular check-out first'
        if existing_attendance['overtime_in']:
            return 'Already started overtime today'
        if current_time < OVERTIME_START_TIME:
            return f'Overtime can only start after {OVERTIME_START_TIME.strftime("%H:%M")}'
        return None

    elif verification_type == 'overtime-out':
        # Overtime-out requires prior overtime-in
        if not existing_attendance or not existing_attendance['overtime_in']:
            return 'Cannot end overtime without starting overtime first'
        if existing_attendance['overtime_out']:
            return 'Already ended overtime today'
        return None

    return 'Invalid verification type'

@app.route('/biometric_attendance', methods=['POST'])
def biometric_attendance():
    """
    DEPRECATED: Handle biometric attendance verification with manual type selection

    This route is now deprecated. Staff should use the biometric device directly,
    and the system will automatically poll for new verifications.
    """
    if 'user_id' not in session:
        return jsonify({'success': False, 'error': 'Not logged in'})

    # Return error message directing users to use the device directly
    return jsonify({
        'success': False,
        'error': 'Please use the biometric device directly to select your verification type and verify your biometric. The system will automatically update your attendance.'
    })

@app.route('/check_device_verification', methods=['POST'])
def check_device_verification():
    """Check for recent biometric verification from the device for a specific staff member"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'error': 'Not logged in'})

    # Allow both staff and admin to use this
    if session['user_type'] not in ['staff', 'admin']:
        return jsonify({'success': False, 'error': 'Unauthorized'})

    # Handle both staff and admin users
    if session['user_type'] == 'staff':
        staff_id = session['user_id']
    else:
        # For admin, get staff_id from form
        staff_id = request.form.get('staff_id')
        if not staff_id:
            return jsonify({'success': False, 'error': 'Staff ID required for admin verification'})

    school_id = session['school_id']
    device_ip = request.form.get('device_ip', '*************')

    current_datetime = datetime.datetime.now()
    db = get_db()

    # Get staff information
    staff_info = db.execute('''
        SELECT staff_id, full_name FROM staff WHERE id = ?
    ''', (staff_id,)).fetchone()

    if not staff_info:
        return jsonify({'success': False, 'error': 'Staff not found'})

    try:
        # Check for recent verification from the device (within last 30 seconds)
        zk_device = ZKBiometricDevice(device_ip)
        if not zk_device.connect():
            return jsonify({
                'success': False,
                'error': 'Failed to connect to biometric device'
            })

        # Look for recent attendance records for this staff member
        recent_cutoff = current_datetime - datetime.timedelta(seconds=30)
        recent_records = zk_device.get_new_attendance_records(recent_cutoff)

        staff_recent_record = None
        for record in recent_records:
            if str(record['user_id']) == str(staff_info['staff_id']):
                staff_recent_record = record
                break

        zk_device.disconnect()

        if not staff_recent_record:
            return jsonify({
                'success': False,
                'error': 'No recent biometric verification found. Please use the biometric device to verify your attendance.'
            })

        # Process the verification from the device
        verification_type = staff_recent_record['verification_type']
        verification_time = staff_recent_record['timestamp']

        # Validate business rules
        today = verification_time.date()
        existing_attendance = db.execute('''
            SELECT * FROM attendance WHERE staff_id = ? AND date = ?
        ''', (staff_id, today)).fetchone()

        validation_error = validate_verification_rules(verification_type, existing_attendance, verification_time.time())
        if validation_error:
            return jsonify({'success': False, 'error': validation_error})

        # Log successful verification
        db.execute('''
            INSERT INTO biometric_verifications
            (staff_id, school_id, verification_type, verification_time, device_ip, biometric_method, verification_status)
            VALUES (?, ?, ?, ?, ?, 'fingerprint', 'success')
        ''', (staff_id, school_id, verification_type, verification_time, device_ip))

        # Update attendance based on verification type
        current_time = verification_time.strftime('%H:%M:%S')
        LATE_ARRIVAL_TIME = datetime.time(9, 0)  # 9:00 AM

        if verification_type == 'check-in':
            status = 'late' if verification_time.time() > LATE_ARRIVAL_TIME else 'present'
            if existing_attendance:
                db.execute('''
                    UPDATE attendance SET time_in = ?, status = ?
                    WHERE staff_id = ? AND date = ?
                ''', (current_time, status, staff_id, today))
            else:
                db.execute('''
                    INSERT INTO attendance (staff_id, school_id, date, time_in, status)
                    VALUES (?, ?, ?, ?, ?)
                ''', (staff_id, school_id, today, current_time, status))

        elif verification_type == 'check-out':
            db.execute('''
                UPDATE attendance SET time_out = ?
                WHERE staff_id = ? AND date = ?
            ''', (current_time, staff_id, today))

        elif verification_type == 'overtime-in':
            if existing_attendance:
                db.execute('''
                    UPDATE attendance SET overtime_in = ?
                    WHERE staff_id = ? AND date = ?
                ''', (current_time, staff_id, today))

        elif verification_type == 'overtime-out':
            db.execute('''
                UPDATE attendance SET overtime_out = ?
                WHERE staff_id = ? AND date = ?
            ''', (current_time, staff_id, today))

        db.commit()

        return jsonify({
            'success': True,
            'message': f'{verification_type.title()} recorded successfully at {current_time}',
            'verification_time': verification_time.strftime('%Y-%m-%d %H:%M:%S'),
            'verification_type': verification_type,
            'biometric_method': 'fingerprint',
            'time_recorded': current_time
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Verification error: {str(e)}'
        })

@app.route('/apply_leave', methods=['POST'])
def apply_leave():
    if 'user_id' not in session or session['user_type'] != 'staff':
        return jsonify({'success': False, 'error': 'Unauthorized'})
    
    staff_id = session['user_id']
    school_id = session['school_id']
    leave_type = request.form.get('leave_type')
    start_date = request.form.get('start_date')
    end_date = request.form.get('end_date')
    reason = request.form.get('reason')
    
    db = get_db()
    
    db.execute('''
        INSERT INTO leave_applications 
        (staff_id, school_id, leave_type, start_date, end_date, reason)
        VALUES (?, ?, ?, ?, ?, ?)
    ''', (staff_id, school_id, leave_type, start_date, end_date, reason))
    db.commit()
    
    return jsonify({'success': True})

@app.route('/process_leave', methods=['POST'])
def process_leave():
    if 'user_id' not in session or session['user_type'] != 'admin':
        return jsonify({'success': False, 'error': 'Unauthorized'})
    
    leave_id = request.form.get('leave_id')
    decision = request.form.get('decision')  # 'approve' or 'reject'
    admin_id = session['user_id']
    processed_at = datetime.datetime.now()
    
    db = get_db()
    
    status = 'approved' if decision == 'approve' else 'rejected'
    
    db.execute('''
        UPDATE leave_applications
        SET status = ?, processed_by = ?, processed_at = ?
        WHERE id = ?
    ''', (status, admin_id, processed_at, leave_id))
    db.commit()
    
    return jsonify({'success': True})

@app.route('/add_staff', methods=['POST'])
def add_staff():
    if 'user_id' not in session or session['user_type'] != 'admin':
        return jsonify({'success': False, 'error': 'Unauthorized'})

    school_id = session['school_id']
    staff_id = request.form.get('staff_id')
    full_name = request.form.get('full_name')
    password = generate_password_hash(request.form.get('password'))
    email = request.form.get('email')
    phone = request.form.get('phone')
    department = request.form.get('department')
    position = request.form.get('position')
    biometric_enrolled = request.form.get('biometric_enrolled', 'false').lower() == 'true'

    # Check if biometric enrollment is required and completed
    if not biometric_enrolled:
        return jsonify({
            'success': False,
            'error': 'Biometric enrollment is required before creating staff account',
            'require_biometric': True
        })

    # Handle photo upload
    photo_url = None
    if 'photo' in request.files:
        photo = request.files['photo']
        if photo.filename != '' and allowed_file(photo.filename):
            try:
                # Ensure uploads directory exists
                upload_dir = os.path.join(app.static_folder, 'uploads')
                os.makedirs(upload_dir, exist_ok=True)

                # Generate unique filename
                ext = os.path.splitext(photo.filename)[1]
                filename = f"staff_{staff_id}_{int(time.time())}{ext}"
                photo_url = os.path.join('uploads', filename)
                photo.save(os.path.join(app.static_folder, photo_url))
            except Exception as e:
                print(f"Error saving photo: {e}")
                return jsonify({'success': False, 'error': 'Error saving photo'})
        elif photo.filename != '':
            return jsonify({'success': False, 'error': 'Invalid file type. Only PNG, JPG, JPEG, and GIF files are allowed.'})

    db = get_db()

    try:
        # Ensure 'photo_url' column exists in the table
        columns = db.execute("PRAGMA table_info(staff)").fetchall()
        has_photo_url = any(col['name'] == 'photo_url' for col in columns)

        if not has_photo_url:
            db.execute("ALTER TABLE staff ADD COLUMN photo_url TEXT")

        db.execute('''
            INSERT INTO staff
            (school_id, staff_id, password_hash, full_name, email, phone, department, position, photo_url)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (school_id, staff_id, password, full_name, email, phone, department, position, photo_url))

        db.commit()
        return jsonify({'success': True})
    except sqlite3.IntegrityError:
        return jsonify({'success': False, 'error': 'Staff ID already exists'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})


@app.route('/delete_school', methods=['POST'])
def delete_school():
    if 'user_id' not in session or session['user_type'] != 'company_admin':
        return jsonify({'success': False, 'error': 'Unauthorized'})
    
    school_id = request.form.get('school_id')
    
    db = get_db()
    
    try:
        # Delete all related records first
        db.execute('DELETE FROM admins WHERE school_id = ?', (school_id,))
        db.execute('DELETE FROM staff WHERE school_id = ?', (school_id,))
        db.execute('DELETE FROM schools WHERE id = ?', (school_id,))
        db.commit()
        return jsonify({'success': True})
    except Exception as e:
        db.rollback()
        return jsonify({'success': False, 'error': str(e)})

@app.route('/update_staff', methods=['POST'])
def update_staff():
    if 'user_id' not in session or session['user_type'] != 'admin':
        return jsonify({'success': False, 'error': 'Unauthorized'})

    staff_id = request.form.get('staff_id')
    full_name = request.form.get('full_name')
    email = request.form.get('email')
    phone = request.form.get('phone')
    department = request.form.get('department')
    position = request.form.get('position')
    status = request.form.get('status')
    school_id = session['school_id']

    if not staff_id or not full_name:
        return jsonify({'success': False, 'error': 'Staff ID and full name are required'})

    db = get_db()

    # Handle photo upload
    photo_url = None
    if 'photo' in request.files:
        photo = request.files['photo']
        if photo.filename != '' and allowed_file(photo.filename):
            try:
                # Ensure uploads directory exists
                upload_dir = os.path.join(app.static_folder, 'uploads')
                os.makedirs(upload_dir, exist_ok=True)

                # Generate unique filename
                ext = os.path.splitext(photo.filename)[1]
                filename = f"staff_{staff_id}_{int(time.time())}{ext}"
                photo_url = os.path.join('uploads', filename)
                photo.save(os.path.join(app.static_folder, photo_url))
            except Exception as e:
                print(f"Error saving photo: {e}")
                return jsonify({'success': False, 'error': 'Error saving photo'})
        elif photo.filename != '':
            return jsonify({'success': False, 'error': 'Invalid file type. Only PNG, JPG, JPEG, and GIF files are allowed.'})

    try:
        # Update staff record
        if photo_url:
            db.execute('''
                UPDATE staff
                SET full_name = ?, email = ?, phone = ?, department = ?, position = ?, photo_url = ?
                WHERE id = ? AND school_id = ?
            ''', (full_name, email, phone, department, position, photo_url, staff_id, school_id))
        else:
            db.execute('''
                UPDATE staff
                SET full_name = ?, email = ?, phone = ?, department = ?, position = ?
                WHERE id = ? AND school_id = ?
            ''', (full_name, email, phone, department, position, staff_id, school_id))

        db.commit()
        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/delete_staff', methods=['POST'])
def delete_staff():
    if 'user_id' not in session or session['user_type'] != 'admin':
        return jsonify({'success': False, 'error': 'Unauthorized'})

    staff_id = request.form.get('staff_id')
    school_id = session['school_id']

    db = get_db()

    db.execute('DELETE FROM staff WHERE id = ? AND school_id = ?', (staff_id, school_id))
    db.commit()

    return jsonify({'success': True})

@app.route('/reset_staff_password', methods=['POST'])
def reset_staff_password():
    """Reset staff password to a default value"""
    if 'user_id' not in session or session['user_type'] != 'admin':
        return jsonify({'success': False, 'error': 'Unauthorized'})

    staff_id = request.form.get('staff_id')
    new_password = request.form.get('new_password', 'password123')  # Default password
    school_id = session['school_id']

    if not staff_id:
        return jsonify({'success': False, 'error': 'Staff ID is required'})

    db = get_db()

    try:
        # Generate new password hash
        password_hash = generate_password_hash(new_password)

        # Update staff password
        db.execute('''
            UPDATE staff
            SET password_hash = ?
            WHERE id = ? AND school_id = ?
        ''', (password_hash, staff_id, school_id))

        db.commit()

        return jsonify({
            'success': True,
            'message': f'Password reset successfully. New password: {new_password}'
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/add_school', methods=['POST'])
def add_school():
    if 'user_id' not in session or session['user_type'] != 'company_admin':
        return jsonify({'success': False, 'error': 'Unauthorized'})

    name = request.form.get('name')
    address = request.form.get('address')
    contact_email = request.form.get('contact_email')
    contact_phone = request.form.get('contact_phone')

    admin_username = request.form.get('admin_username')
    admin_password = generate_password_hash(request.form.get('admin_password'))
    admin_full_name = request.form.get('admin_full_name')
    admin_email = request.form.get('admin_email')

    db = get_db()

    # Handle logo upload
    logo_url = None
    if 'logo' in request.files:
        logo = request.files['logo']
        if logo.filename != '' and allowed_file(logo.filename):
            try:
                upload_dir = os.path.join(app.static_folder, 'school_logos')
                os.makedirs(upload_dir, exist_ok=True)

                ext = os.path.splitext(logo.filename)[1]
                filename = f"school_{int(time.time())}{ext}"
                logo_url = os.path.join('school_logos', filename)
                logo.save(os.path.join(app.static_folder, logo_url))
            except Exception as e:
                print(f"Error saving logo: {e}")
                return jsonify({'success': False, 'error': 'Failed to save school logo'})
        elif logo.filename != '':
            return jsonify({'success': False, 'error': 'Invalid file type. Only PNG, JPG, JPEG, and GIF files are allowed.'})

    try:
        cursor = db.cursor()

        # Add school
        cursor.execute('''
            INSERT INTO schools (name, address, contact_email, contact_phone, logo_url)
            VALUES (?, ?, ?, ?, ?)
        ''', (name, address, contact_email, contact_phone, logo_url))
        school_id = cursor.lastrowid

        # Add initial admin for the school
        cursor.execute('''
            INSERT INTO admins (school_id, username, password, full_name, email)
            VALUES (?, ?, ?, ?, ?)
        ''', (school_id, admin_username, admin_password, admin_full_name, admin_email))

        db.commit()
        return jsonify({'success': True})
    
    except sqlite3.IntegrityError:
        db.rollback()
        return jsonify({'success': False, 'error': 'School or admin username already exists'})
    
    except Exception as e:
        db.rollback()
        return jsonify({'success': False, 'error': str(e)})

@app.route('/admin/staff/<int:id>')
def staff_profile(id):
    if 'user_id' not in session or session['user_type'] != 'admin':
        return redirect(url_for('index'))

    db = get_db()
    staff = db.execute('SELECT * FROM staff WHERE id = ? AND school_id = ?',
                      (id, session['school_id'])).fetchone()

    if not staff:
        return redirect(url_for('admin_dashboard'))

    # Get attendance summary for this staff member
    attendance_summary = db.execute('''
        SELECT
            COUNT(CASE WHEN status = 'present' THEN 1 END) as present_count,
            COUNT(CASE WHEN status = 'late' THEN 1 END) as late_count,
            COUNT(CASE WHEN status = 'absent' THEN 1 END) as absent_count,
            COUNT(CASE WHEN status = 'leave' THEN 1 END) as leave_count
        FROM attendance
        WHERE staff_id = ? AND date >= date('now', '-30 days')
    ''', (id,)).fetchone()

    # Get recent attendance records
    recent_attendance = db.execute('''
        SELECT date, time_in, time_out, overtime_in, overtime_out, status
        FROM attendance
        WHERE staff_id = ?
        ORDER BY date DESC
        LIMIT 10
    ''', (id,)).fetchall()

    # Get recent biometric verifications
    recent_verifications = db.execute('''
        SELECT verification_type, verification_time, biometric_method, verification_status
        FROM biometric_verifications
        WHERE staff_id = ?
        ORDER BY verification_time DESC
        LIMIT 10
    ''', (id,)).fetchall()

    return render_template('staff_profile.html',
                         staff=staff,
                         attendance_summary=attendance_summary,
                         recent_attendance=recent_attendance,
                         recent_verifications=recent_verifications)

@app.route('/admin/search_staff')
def search_staff():
    if 'user_id' not in session or session['user_type'] != 'admin':
        return jsonify({'success': False, 'error': 'Unauthorized'})
    
    search_term = request.args.get('q', '')
    db = get_db()
    
    staff = db.execute('''
        SELECT id, staff_id, full_name, department, position 
        FROM staff 
        WHERE school_id = ? AND full_name LIKE ?
        ORDER BY full_name
    ''', (session['school_id'], f"%{search_term}%")).fetchall()
    
    return render_template('admin_dashboard.html', 
                         staff=staff,
                         pending_leaves=get_pending_leaves(),
                         attendance_summary=get_today_attendance(),
                         today=datetime.date.today())

# Update in app.py
@app.route('/toggle_school_visibility', methods=['POST'])
def toggle_school_visibility():
    if 'user_id' not in session or session['user_type'] != 'company_admin':
        return jsonify({'success': False, 'error': 'Unauthorized'})
    
    school_id = request.form.get('school_id')
    db = get_db()
    
    # Ensure column exists
    columns = db.execute("PRAGMA table_info(schools)").fetchall()
    has_is_hidden = any(col['name'] == 'is_hidden' for col in columns)
    
    if not has_is_hidden:
        db.execute('ALTER TABLE schools ADD COLUMN is_hidden BOOLEAN DEFAULT 0')
        db.commit()
    
    # Toggle visibility
    db.execute('''
        UPDATE schools 
        SET is_hidden = CASE WHEN is_hidden = 1 THEN 0 ELSE 1 END
        WHERE id = ?
    ''', (school_id,))
    db.commit()
    
    return jsonify({'success': True})




# ZK Biometric Device Integration Routes
@app.route('/sync_biometric_attendance', methods=['POST'])
def sync_biometric_attendance():
    """Sync attendance data from ZK biometric device"""
    if 'user_id' not in session or session['user_type'] not in ['admin', 'company_admin']:
        return jsonify({'success': False, 'error': 'Unauthorized'})

    device_ip = request.form.get('device_ip', '*************')
    school_id = session.get('school_id', 1)

    try:
        result = sync_attendance_from_device(device_ip, school_id)
        return jsonify(result)
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Sync failed: {str(e)}',
            'total_records': 0,
            'sqlite_synced': 0,
            'mysql_synced': 0
        })

@app.route('/test_biometric_connection', methods=['POST'])
def test_biometric_connection():
    """Test connection to ZK biometric device"""
    if 'user_id' not in session or session['user_type'] not in ['admin', 'company_admin']:
        return jsonify({'success': False, 'error': 'Unauthorized'})

    device_ip = request.form.get('device_ip', '*************')

    try:
        zk_device = ZKBiometricDevice(device_ip)
        if zk_device.connect():
            # Get device info
            users = zk_device.get_users()
            zk_device.disconnect()

            return jsonify({
                'success': True,
                'message': 'Connection successful',
                'device_ip': device_ip,
                'total_users': len(users)
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Failed to connect to device',
                'device_ip': device_ip
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Connection test failed: {str(e)}',
            'device_ip': device_ip
        })

@app.route('/get_biometric_users', methods=['GET'])
def get_biometric_users():
    """Get users from ZK biometric device"""
    if 'user_id' not in session or session['user_type'] not in ['admin', 'company_admin']:
        return jsonify({'success': False, 'error': 'Unauthorized'})

    device_ip = request.args.get('device_ip', '*************')

    try:
        zk_device = ZKBiometricDevice(device_ip)
        if zk_device.connect():
            users = zk_device.get_users()
            zk_device.disconnect()

            return jsonify({
                'success': True,
                'users': users,
                'total_users': len(users)
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Failed to connect to device'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to get users: {str(e)}'
        })

@app.route('/enroll_biometric_user', methods=['POST'])
def enroll_biometric_user():
    """Enroll a user in the ZK biometric device"""
    if 'user_id' not in session or session['user_type'] not in ['admin', 'company_admin']:
        return jsonify({'success': False, 'error': 'Unauthorized'})

    device_ip = request.form.get('device_ip', '*************')
    user_id = request.form.get('user_id')
    name = request.form.get('name')
    privilege = int(request.form.get('privilege', 0))
    overwrite = request.form.get('overwrite', 'false').lower() == 'true'

    if not user_id or not name:
        return jsonify({'success': False, 'message': 'User ID and name are required'})

    try:
        zk_device = ZKBiometricDevice(device_ip)
        if zk_device.connect():
            result = zk_device.enroll_user(user_id, name, privilege, overwrite=overwrite)
            zk_device.disconnect()

            if result['success']:
                return jsonify({
                    'success': True,
                    'message': result['message'],
                    'action': result.get('action', 'enrolled'),
                    'user_exists': result.get('user_exists', False)
                })
            else:
                return jsonify({
                    'success': False,
                    'message': result['message'],
                    'user_exists': result.get('user_exists', False),
                    'existing_user': result.get('existing_user')
                })
        else:
            return jsonify({
                'success': False,
                'message': 'Failed to connect to device'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Enrollment failed: {str(e)}'
        })

@app.route('/check_biometric_user', methods=['POST'])
def check_biometric_user():
    """Check if a user already exists on the ZK biometric device"""
    if 'user_id' not in session or session['user_type'] not in ['admin', 'company_admin']:
        return jsonify({'success': False, 'error': 'Unauthorized'})

    device_ip = request.form.get('device_ip', '*************')
    user_id = request.form.get('user_id')

    if not user_id:
        return jsonify({'success': False, 'message': 'User ID is required'})

    try:
        zk_device = ZKBiometricDevice(device_ip)
        if zk_device.connect():
            users = zk_device.get_users()
            zk_device.disconnect()

            # Check if user exists
            for user in users:
                if user['user_id'] == user_id:
                    return jsonify({
                        'success': True,
                        'user_exists': True,
                        'user_data': {
                            'user_id': user['user_id'],
                            'name': user['name'],
                            'privilege': user['privilege']
                        }
                    })

            return jsonify({
                'success': True,
                'user_exists': False,
                'message': f'User {user_id} not found on device'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Failed to connect to device'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Check failed: {str(e)}'
        })

@app.route('/start_biometric_enrollment', methods=['POST'])
def start_biometric_enrollment():
    """Start biometric enrollment mode on device"""
    if 'user_id' not in session or session['user_type'] not in ['admin', 'company_admin']:
        return jsonify({'success': False, 'error': 'Unauthorized'})

    device_ip = request.form.get('device_ip', '*************')

    try:
        zk_device = ZKBiometricDevice(device_ip)
        if zk_device.connect():
            success = zk_device.start_enrollment_mode()
            # Don't disconnect here - keep connection for enrollment

            if success:
                return jsonify({
                    'success': True,
                    'message': 'Device ready for biometric enrollment'
                })
            else:
                zk_device.disconnect()
                return jsonify({
                    'success': False,
                    'message': 'Failed to start enrollment mode'
                })
        else:
            return jsonify({
                'success': False,
                'message': 'Failed to connect to device'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to start enrollment: {str(e)}'
        })

@app.route('/end_biometric_enrollment', methods=['POST'])
def end_biometric_enrollment():
    """End biometric enrollment mode on device"""
    if 'user_id' not in session or session['user_type'] not in ['admin', 'company_admin']:
        return jsonify({'success': False, 'error': 'Unauthorized'})

    device_ip = request.form.get('device_ip', '*************')

    try:
        zk_device = ZKBiometricDevice(device_ip)
        if zk_device.connect():
            success = zk_device.end_enrollment_mode()
            zk_device.disconnect()

            if success:
                return jsonify({
                    'success': True,
                    'message': 'Enrollment mode ended'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': 'Failed to end enrollment mode'
                })
        else:
            return jsonify({
                'success': False,
                'message': 'Failed to connect to device'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to end enrollment: {str(e)}'
        })

@app.route('/verify_biometric_enrollment', methods=['POST'])
def verify_biometric_enrollment():
    """Verify that biometric data was captured for a user"""
    if 'user_id' not in session or session['user_type'] not in ['admin', 'company_admin']:
        return jsonify({'success': False, 'error': 'Unauthorized'})

    device_ip = request.form.get('device_ip', '*************')
    user_id = request.form.get('user_id')
    trigger_enrollment = request.form.get('trigger_enrollment', 'false').lower() == 'true'

    if not user_id:
        return jsonify({'success': False, 'message': 'User ID is required'})

    try:
        zk_device = ZKBiometricDevice(device_ip)
        if zk_device.connect():
            # First check if user exists
            users = zk_device.get_users()
            user_exists = False
            user_data = None

            for user in users:
                if user['user_id'] == user_id:
                    user_exists = True
                    user_data = user
                    break

            # If user doesn't exist or we need to trigger enrollment
            if trigger_enrollment and user_exists:
                # Trigger biometric enrollment for the user
                result = zk_device.trigger_biometric_enrollment(user_id)

                if result['success']:
                    return jsonify({
                        'success': True,
                        'enrolled': False,
                        'enrollment_started': True,
                        'manual_mode': result.get('manual_mode', True),
                        'message': result['message']
                    })
                else:
                    zk_device.disconnect()
                    return jsonify({
                        'success': False,
                        'message': result['message']
                    })

            # Check if user exists and has biometric data
            if user_exists:
                zk_device.disconnect()
                return jsonify({
                    'success': True,
                    'enrolled': True,
                    'user_data': user_data,
                    'message': f'User {user_id} biometric data verified'
                })
            else:
                # User not found, try to create and enroll
                if trigger_enrollment:
                    # First create the user
                    name = request.form.get('name', f'User {user_id}')
                    privilege = int(request.form.get('privilege', 0))

                    # Create user first
                    enroll_result = zk_device.enroll_user(user_id, name, privilege)

                    if enroll_result['success']:
                        # Now trigger biometric enrollment
                        result = zk_device.trigger_biometric_enrollment(user_id)

                        if result['success']:
                            return jsonify({
                                'success': True,
                                'enrolled': False,
                                'user_created': True,
                                'enrollment_started': True,
                                'manual_mode': result.get('manual_mode', True),
                                'message': f'User created and {result["message"]}'
                            })

                    zk_device.disconnect()
                    return jsonify({
                        'success': False,
                        'message': f'Failed to create user: {enroll_result.get("message", "Unknown error")}'
                    })

                zk_device.disconnect()
                return jsonify({
                    'success': True,
                    'enrolled': False,
                    'message': f'User {user_id} not found or biometric data not captured'
                })
        else:
            return jsonify({
                'success': False,
                'message': 'Failed to connect to device'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Verification failed: {str(e)}'
        })

@app.route('/delete_biometric_user', methods=['POST'])
def delete_biometric_user():
    """Delete a user from the ZK biometric device"""
    if 'user_id' not in session or session['user_type'] not in ['admin', 'company_admin']:
        return jsonify({'success': False, 'error': 'Unauthorized'})

    device_ip = request.form.get('device_ip', '*************')
    user_id = request.form.get('user_id')

    if not user_id:
        return jsonify({'success': False, 'message': 'User ID is required'})

    try:
        zk_device = ZKBiometricDevice(device_ip)
        if zk_device.connect():
            success = zk_device.delete_user(user_id)
            zk_device.disconnect()

            if success:
                return jsonify({
                    'success': True,
                    'message': f'User {user_id} deleted successfully from device'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': f'User {user_id} not found on device or deletion failed'
                })
        else:
            return jsonify({
                'success': False,
                'message': 'Failed to connect to device'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Deletion failed: {str(e)}'
        })

@app.route('/poll_device_attendance', methods=['POST'])
def poll_device_attendance():
    """Poll ZK device for new attendance records and process them automatically"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'error': 'Unauthorized'})

    device_ip = request.form.get('device_ip', '*************')
    school_id = session.get('school_id', 1)

    try:
        result = process_device_attendance_automatically(device_ip, school_id)
        return jsonify(result)
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Polling failed: {str(e)}',
            'processed_count': 0
        })

@app.route('/get_latest_device_verifications')
def get_latest_device_verifications():
    """Get the latest biometric verifications from the device for real-time updates"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'error': 'Unauthorized'})

    device_ip = request.args.get('device_ip', '*************')
    since_minutes = int(request.args.get('since_minutes', 5))  # Default to last 5 minutes

    try:
        zk_device = ZKBiometricDevice(device_ip)
        if not zk_device.connect():
            return jsonify({
                'success': False,
                'error': 'Failed to connect to biometric device',
                'verifications': []
            })

        # Get records from the last few minutes
        since_timestamp = datetime.datetime.now() - datetime.timedelta(minutes=since_minutes)
        recent_records = zk_device.get_new_attendance_records(since_timestamp)

        zk_device.disconnect()

        # Format the records for the frontend
        verifications = []
        for record in recent_records:
            verifications.append({
                'user_id': record['user_id'],
                'verification_type': record['verification_type'],
                'timestamp': record['timestamp'].strftime('%Y-%m-%d %H:%M:%S'),
                'time_only': record['timestamp'].strftime('%H:%M:%S')
            })

        return jsonify({
            'success': True,
            'verifications': verifications,
            'count': len(verifications)
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Failed to get latest verifications: {str(e)}',
            'verifications': []
        })

@app.route('/staff/profile')
def staff_profile_page():
    """Staff profile page with personal information and attendance history"""
    if 'user_id' not in session or session['user_type'] != 'staff':
        return redirect(url_for('index'))

    db = get_db()
    staff_id = session['user_id']

    # Get staff information
    staff = db.execute('''
        SELECT * FROM staff WHERE id = ?
    ''', (staff_id,)).fetchone()

    if not staff:
        return redirect(url_for('index'))

    # Get attendance summary for current month
    today = datetime.date.today()
    first_day = today.replace(day=1)
    last_day = (today.replace(day=28) + datetime.timedelta(days=4)).replace(day=1) - datetime.timedelta(days=1)

    attendance_summary = db.execute('''
        SELECT
            COUNT(*) as total_days,
            SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_days,
            SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_days,
            SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_days,
            SUM(CASE WHEN status = 'leave' THEN 1 ELSE 0 END) as leave_days
        FROM attendance
        WHERE staff_id = ? AND date BETWEEN ? AND ?
    ''', (staff_id, first_day, last_day)).fetchone()

    # Get recent attendance records
    recent_attendance = db.execute('''
        SELECT date, time_in, time_out, overtime_in, overtime_out, status
        FROM attendance
        WHERE staff_id = ?
        ORDER BY date DESC
        LIMIT 30
    ''', (staff_id,)).fetchall()

    # Get leave applications
    leave_applications = db.execute('''
        SELECT id, leave_type, start_date, end_date, reason, status, applied_at
        FROM leave_applications
        WHERE staff_id = ?
        ORDER BY applied_at DESC
        LIMIT 10
    ''', (staff_id,)).fetchall()

    # Get recent biometric verifications
    recent_verifications = db.execute('''
        SELECT verification_type, verification_time, biometric_method, verification_status
        FROM biometric_verifications
        WHERE staff_id = ?
        ORDER BY verification_time DESC
        LIMIT 20
    ''', (staff_id,)).fetchall()

    return render_template('staff_my_profile.html',
                         staff=staff,
                         attendance_summary=attendance_summary,
                         recent_attendance=recent_attendance,
                         leave_applications=leave_applications,
                         recent_verifications=recent_verifications,
                         today=today,
                         current_month=today.strftime('%B %Y'))

@app.route('/staff/update_profile', methods=['POST'])
def update_staff_profile():
    """Update staff profile information"""
    if 'user_id' not in session or session['user_type'] != 'staff':
        return jsonify({'success': False, 'error': 'Unauthorized'})

    staff_id = session['user_id']
    email = request.form.get('email')
    phone = request.form.get('phone')

    db = get_db()

    try:
        # Handle photo upload
        photo_url = None
        if 'photo' in request.files:
            photo = request.files['photo']
            if photo.filename != '' and allowed_file(photo.filename):
                try:
                    # Ensure uploads directory exists
                    upload_dir = os.path.join(app.static_folder, 'uploads')
                    os.makedirs(upload_dir, exist_ok=True)

                    # Generate unique filename
                    ext = os.path.splitext(photo.filename)[1]
                    filename = f"staff_{staff_id}_{int(time.time())}{ext}"
                    photo_url = os.path.join('uploads', filename)
                    photo.save(os.path.join(app.static_folder, photo_url))
                except Exception as e:
                    return jsonify({'success': False, 'error': 'Error saving photo'})

        # Update profile
        if photo_url:
            db.execute('''
                UPDATE staff SET email = ?, phone = ?, photo_url = ?
                WHERE id = ?
            ''', (email, phone, photo_url, staff_id))
        else:
            db.execute('''
                UPDATE staff SET email = ?, phone = ?
                WHERE id = ?
            ''', (email, phone, staff_id))

        db.commit()
        return jsonify({'success': True, 'message': 'Profile updated successfully'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/staff/change_password', methods=['POST'])
def change_staff_password():
    """Change staff password"""
    if 'user_id' not in session or session['user_type'] != 'staff':
        return jsonify({'success': False, 'error': 'Unauthorized'})

    staff_id = session['user_id']
    current_password = request.form.get('current_password')
    new_password = request.form.get('new_password')
    confirm_password = request.form.get('confirm_password')

    if new_password != confirm_password:
        return jsonify({'success': False, 'error': 'New passwords do not match'})

    db = get_db()

    # Get current password hash
    staff = db.execute('SELECT password_hash FROM staff WHERE id = ?', (staff_id,)).fetchone()

    if not staff or not check_password_hash(staff['password_hash'], current_password):
        return jsonify({'success': False, 'error': 'Current password is incorrect'})

    try:
        # Update password
        new_password_hash = generate_password_hash(new_password)
        db.execute('''
            UPDATE staff SET password_hash = ?
            WHERE id = ?
        ''', (new_password_hash, staff_id))

        db.commit()
        return jsonify({'success': True, 'message': 'Password changed successfully'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/staff/attendance_calendar')
def staff_attendance_calendar():
    """Get attendance data for calendar view"""
    if 'user_id' not in session or session['user_type'] != 'staff':
        return jsonify({'success': False, 'error': 'Unauthorized'})

    staff_id = session['user_id']
    start_date = request.args.get('start')
    end_date = request.args.get('end')

    db = get_db()

    # Get attendance records for the date range
    attendance = db.execute('''
        SELECT date, time_in, time_out, overtime_in, overtime_out, status, notes
        FROM attendance
        WHERE staff_id = ? AND date BETWEEN ? AND ?
        ORDER BY date
    ''', (staff_id, start_date, end_date)).fetchall()

    # Get leave applications for the date range
    leaves = db.execute('''
        SELECT start_date, end_date, leave_type, status
        FROM leave_applications
        WHERE staff_id = ? AND status = 'approved'
        AND ((start_date BETWEEN ? AND ?) OR (end_date BETWEEN ? AND ?)
        OR (start_date <= ? AND end_date >= ?))
    ''', (staff_id, start_date, end_date, start_date, end_date, start_date, end_date)).fetchall()

    return jsonify({
        'success': True,
        'attendance': [dict(a) for a in attendance],
        'leaves': [dict(l) for l in leaves]
    })

@app.route('/logout')
def logout():
    session.clear()
    return redirect(url_for('index'))

if __name__ == '__main__':
    app.run(debug=True)