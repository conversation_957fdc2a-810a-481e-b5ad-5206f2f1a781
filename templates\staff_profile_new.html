<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Staff Profile - {{ staff.full_name }} - VishnoRex</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/fullcalendar@5.11.3/main.min.css">
    <style>
        .profile-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
        }
        .profile-photo {
            width: 150px;
            height: 150px;
            object-fit: cover;
            border: 5px solid white;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        .stat-card:hover {
            transform: translateY(-2px);
        }
        .attendance-calendar {
            height: 400px;
        }
        .verification-badge {
            font-size: 0.8rem;
        }
        .timeline-item {
            border-left: 3px solid #007bff;
            padding-left: 1rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('admin_dashboard') }}">
                <i class="bi bi-arrow-left"></i> Back to Dashboard
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text">Admin: {{ session.get('username', 'Unknown') }}</span>
                <a class="nav-link" href="{{ url_for('logout') }}">
                    <i class="bi bi-box-arrow-right"></i> Logout
                </a>
            </div>
        </div>
    </nav>

    <!-- Profile Header -->
    <div class="profile-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-3 text-center">
                    {% if staff.photo_url %}
                        <img src="{{ url_for('static', filename=staff.photo_url) }}" 
                             alt="Profile Photo" class="rounded-circle profile-photo">
                    {% else %}
                        <div class="bg-light rounded-circle profile-photo d-inline-flex align-items-center justify-content-center">
                            <i class="bi bi-person-fill text-muted" style="font-size: 4rem;"></i>
                        </div>
                    {% endif %}
                </div>
                <div class="col-md-6">
                    <h1 class="mb-2">{{ staff.full_name }}</h1>
                    <h5 class="mb-1 opacity-75">{{ staff.position }}</h5>
                    <p class="mb-1 opacity-75">{{ staff.department }}</p>
                    <p class="mb-0 opacity-75">
                        <i class="bi bi-person-badge"></i> Staff ID: {{ staff.staff_id }}
                    </p>
                </div>
                <div class="col-md-3 text-end">
                    <div class="btn-group-vertical" role="group">
                        <button type="button" class="btn btn-light btn-sm mb-2" data-bs-toggle="modal" data-bs-target="#editStaffModal">
                            <i class="bi bi-pencil-square"></i> Edit Profile
                        </button>
                        <button type="button" class="btn btn-warning btn-sm mb-2" id="resetPasswordBtn">
                            <i class="bi bi-key"></i> Reset Password
                        </button>
                        <button type="button" class="btn btn-danger btn-sm" id="deleteStaffBtn">
                            <i class="bi bi-trash"></i> Delete Staff
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row">
            <!-- Left Column - Personal Information -->
            <div class="col-md-4">
                <!-- Contact Information -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-person-lines-fill"></i> Contact Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Email:</label>
                            <p class="mb-0">{{ staff.email or 'Not provided' }}</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">Phone:</label>
                            <p class="mb-0">{{ staff.phone or 'Not provided' }}</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">Department:</label>
                            <p class="mb-0">{{ staff.department }}</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">Position:</label>
                            <p class="mb-0">{{ staff.position }}</p>
                        </div>
                        <div class="mb-0">
                            <label class="form-label fw-bold">Joined Date:</label>
                            <p class="mb-0">{{ staff.created_at|dateformat if staff.created_at else 'Unknown' }}</p>
                        </div>
                    </div>
                </div>

                <!-- Attendance Summary -->
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="bi bi-calendar-check"></i> Attendance Summary (Last 30 Days)</h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="stat-card bg-success text-white">
                                    <h3 class="mb-1">{{ attendance_summary.present_count or 0 }}</h3>
                                    <small>Present</small>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="stat-card bg-warning text-white">
                                    <h3 class="mb-1">{{ attendance_summary.late_count or 0 }}</h3>
                                    <small>Late</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="stat-card bg-danger text-white">
                                    <h3 class="mb-1">{{ attendance_summary.absent_count or 0 }}</h3>
                                    <small>Absent</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="stat-card bg-info text-white">
                                    <h3 class="mb-1">{{ attendance_summary.leave_count or 0 }}</h3>
                                    <small>Leave</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Biometric Verifications -->
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="bi bi-fingerprint"></i> Recent Biometric Verifications</h5>
                    </div>
                    <div class="card-body">
                        {% if recent_verifications %}
                            {% for verification in recent_verifications[:5] %}
                            <div class="timeline-item">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <strong>{{ verification.verification_type|title }}</strong>
                                        <br>
                                        <small class="text-muted">{{ verification.biometric_method|title }}</small>
                                    </div>
                                    <div class="text-end">
                                        {% if verification.verification_status == 'success' %}
                                            <span class="badge bg-success verification-badge">Success</span>
                                        {% else %}
                                            <span class="badge bg-danger verification-badge">Failed</span>
                                        {% endif %}
                                        <br>
                                        <small class="text-muted">{{ verification.verification_time|dateformat }}</small>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                            <p class="text-muted mb-0">No recent biometric verifications found.</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Right Column - Attendance Details -->
            <div class="col-md-8">
                <!-- Attendance Calendar -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-calendar3"></i> Attendance Calendar</h5>
                    </div>
                    <div class="card-body">
                        <div id="attendanceCalendar" class="attendance-calendar"></div>
                    </div>
                </div>

                <!-- Recent Attendance Records -->
                <div class="card mb-4">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0"><i class="bi bi-clock-history"></i> Recent Attendance Records</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Time In</th>
                                        <th>Time Out</th>
                                        <th>Overtime In</th>
                                        <th>Overtime Out</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% if recent_attendance %}
                                        {% for record in recent_attendance %}
                                        <tr>
                                            <td>{{ record.date|dateformat }}</td>
                                            <td>{{ record.time_in or '--:--' }}</td>
                                            <td>{{ record.time_out or '--:--' }}</td>
                                            <td>{{ record.overtime_in or '--:--' }}</td>
                                            <td>{{ record.overtime_out or '--:--' }}</td>
                                            <td>
                                                {% if record.status == 'present' %}
                                                    <span class="badge bg-success">Present</span>
                                                {% elif record.status == 'late' %}
                                                    <span class="badge bg-warning">Late</span>
                                                {% elif record.status == 'absent' %}
                                                    <span class="badge bg-danger">Absent</span>
                                                {% elif record.status == 'leave' %}
                                                    <span class="badge bg-info">Leave</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">{{ record.status|title }}</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    {% else %}
                                        <tr>
                                            <td colspan="6" class="text-center text-muted">No attendance records found.</td>
                                        </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Staff Modal -->
    <div class="modal fade" id="editStaffModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Staff Profile</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="editStaffForm">
                    <div class="modal-body">
                        <input type="hidden" id="editStaffId" value="{{ staff.id }}">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="editFullName" class="form-label">Full Name</label>
                                <input type="text" class="form-control" id="editFullName" value="{{ staff.full_name }}" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="editStaffIdField" class="form-label">Staff ID</label>
                                <input type="text" class="form-control" id="editStaffIdField" value="{{ staff.staff_id }}" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="editEmail" class="form-label">Email</label>
                                <input type="email" class="form-control" id="editEmail" value="{{ staff.email or '' }}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="editPhone" class="form-label">Phone</label>
                                <input type="tel" class="form-control" id="editPhone" value="{{ staff.phone or '' }}">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="editDepartment" class="form-label">Department</label>
                                <input type="text" class="form-control" id="editDepartment" value="{{ staff.department or '' }}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="editPosition" class="form-label">Position</label>
                                <input type="text" class="form-control" id="editPosition" value="{{ staff.position or '' }}">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="editPhoto" class="form-label">Profile Photo</label>
                            <input type="file" class="form-control" id="editPhoto" accept="image/*">
                            <small class="form-text text-muted">Leave empty to keep current photo</small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary" id="saveEditStaff">Save Changes</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/fullcalendar@5.11.3/main.min.js"></script>
    <script>
        // CSRF Token
        function getCSRFToken() {
            return document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
        }

        // Initialize Calendar
        document.addEventListener('DOMContentLoaded', function() {
            const calendarEl = document.getElementById('attendanceCalendar');
            const calendar = new FullCalendar.Calendar(calendarEl, {
                initialView: 'dayGridMonth',
                headerToolbar: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'dayGridMonth,listWeek'
                },
                events: [
                    {% for record in recent_attendance %}
                    {
                        title: '{{ record.status|title }}{% if record.time_in %} ({{ record.time_in }}){% endif %}',
                        start: '{{ record.date }}',
                        allDay: true,
                        backgroundColor: {% if record.status == 'present' %}'#198754'{% elif record.status == 'late' %}'#ffc107'{% elif record.status == 'absent' %}'#dc3545'{% elif record.status == 'leave' %}'#0dcaf0'{% else %}'#6c757d'{% endif %},
                        borderColor: {% if record.status == 'present' %}'#198754'{% elif record.status == 'late' %}'#ffc107'{% elif record.status == 'absent' %}'#dc3545'{% elif record.status == 'leave' %}'#0dcaf0'{% else %}'#6c757d'{% endif %}
                    }{% if not loop.last %},{% endif %}
                    {% endfor %}
                ]
            });
            calendar.render();
        });

        // Reset Password
        document.getElementById('resetPasswordBtn').addEventListener('click', function() {
            if (confirm('Are you sure you want to reset the password for {{ staff.full_name }}?')) {
                fetch('/reset_staff_password', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `staff_id={{ staff.id }}&csrf_token=${encodeURIComponent(getCSRFToken())}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(`Password reset successfully! New temporary password: ${data.temp_password}`);
                    } else {
                        alert(data.error || 'Failed to reset password');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while resetting password');
                });
            }
        });

        // Delete Staff
        document.getElementById('deleteStaffBtn').addEventListener('click', function() {
            if (confirm('Are you sure you want to delete {{ staff.full_name }}? This action cannot be undone.')) {
                fetch('/delete_staff', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `staff_id={{ staff.id }}&csrf_token=${encodeURIComponent(getCSRFToken())}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Staff deleted successfully');
                        window.location.href = '{{ url_for("admin_dashboard") }}';
                    } else {
                        alert(data.error || 'Failed to delete staff');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while deleting staff');
                });
            }
        });

        // Edit Staff Form
        document.getElementById('editStaffForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData();
            formData.append('staff_id', document.getElementById('editStaffId').value);
            formData.append('full_name', document.getElementById('editFullName').value);
            formData.append('staff_id_field', document.getElementById('editStaffIdField').value);
            formData.append('email', document.getElementById('editEmail').value);
            formData.append('phone', document.getElementById('editPhone').value);
            formData.append('department', document.getElementById('editDepartment').value);
            formData.append('position', document.getElementById('editPosition').value);
            formData.append('csrf_token', getCSRFToken());

            const photoFile = document.getElementById('editPhoto').files[0];
            if (photoFile) {
                formData.append('photo', photoFile);
            }

            fetch('/edit_staff', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Staff profile updated successfully');
                    location.reload();
                } else {
                    alert(data.error || 'Failed to update staff profile');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while updating staff profile');
            });
        });
    </script>
</body>
</html>
